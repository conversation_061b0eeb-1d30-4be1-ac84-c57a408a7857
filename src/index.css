@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Dark grey theme as default */
    --background: 220 13% 18%;
    --foreground: 220 9% 85%;

    --muted: 220 13% 22%;
    --muted-foreground: 220 9% 65%;

    --popover: 220 13% 20%;
    --popover-foreground: 220 9% 85%;

    --border: 220 13% 28%;
    --input: 220 13% 28%;

    --card: 220 13% 20%;
    --card-foreground: 220 9% 85%;

    --primary: 220 9% 85%;
    --primary-foreground: 220 13% 15%;

    --secondary: 220 13% 25%;
    --secondary-foreground: 220 9% 85%;

    --accent: 220 13% 25%;
    --accent-foreground: 220 9% 85%;

    --destructive: 0 63% 50%;
    --destructive-foreground: 220 9% 85%;

    --ring: 220 13% 35%;

    --radius: 0.5rem;

    --font-sans: 'Inter', sans-serif;
  }

  .dark {
    /* Light theme for potential switching */
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html{
    font-size: 13px; /* Default is 16px */
  }
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings:
            "rlig" 1,
            "calt" 1;
  }
}
